<?php

namespace App\Models;

use App\Enums\ChurchLevel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class Church extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name', 'level', 'location', 'phone_number', 'address', 'email', 'district', 'region',
        'date_established', 'parent_church_id',
        'bank_name', 'bank_account_number', 'bank_account_name',
        'mobile_money_number', 'mobile_money_provider', 'current_balance',
        'youth_count', 'young_adults_count', 'children_count', 'elders_count',
    ];

    protected $casts = [
        'date_established' => 'date',
        'level' => ChurchLevel::class,
        'current_balance' => 'decimal:2',
    ];

    /**
     * Get the age of the church in years
     */
    public function getAgeInYears(): int
    {
        if (!$this->date_established) {
            return 0;
        }

        return $this->date_established->diffInYears(now());
    }

    public function parentChurch()
    {
        return $this->belongsTo(Church::class, 'parent_church_id');
    }

    public function childChurches()
    {
        return $this->hasMany(Church::class, 'parent_church_id');
    }

    public function leaders()
    {
        return $this->hasMany(ChurchLeader::class);
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function requests()
    {
        return $this->hasMany(Request::class);
    }

    // Financial Relationships
    public function financialBalance()
    {
        return $this->hasOne(FinancialBalance::class);
    }

    public function contributions()
    {
        return $this->hasMany(Contribution::class, 'created_by_church_id');
    }

    public function incomingTransactions()
    {
        return $this->hasMany(Transaction::class, 'to_church_id');
    }

    public function outgoingTransactions()
    {
        return $this->hasMany(Transaction::class, 'from_church_id');
    }

    public function receipts()
    {
        return $this->hasMany(Receipt::class, 'issued_by_church_id');
    }

    public function financialReports()
    {
        return $this->hasMany(FinancialReport::class);
    }

    // Validation methods
    public function validateHierarchy(): bool
    {
        if (!$this->parent_church_id) {
            // Root church (National) should have no parent
            return $this->level === ChurchLevel::NATIONAL;
        }

        $parent = $this->parentChurch;
        if (!$parent) {
            return false;
        }

        // Check if parent level can have this child level
        return $parent->level->canBeParentOf($this->level);
    }

    public function canHaveChildLevel(ChurchLevel $childLevel): bool
    {
        return $this->level->canBeParentOf($childLevel);
    }

    public function getValidChildLevels(): array
    {
        return $this->level->getValidChildLevels();
    }

    public function getApprovalAuthority(): ?Church
    {
        $authorityLevel = ChurchLevel::getApprovalAuthority($this->level);

        if (!$authorityLevel) {
            return null;
        }

        // Find the parent church at the authority level
        $current = $this->parentChurch;
        while ($current && $current->level !== $authorityLevel) {
            $current = $current->parentChurch;
        }

        return $current;
    }

    public function canApproveRequestsFor(Church $requestingChurch): bool
    {
        // Can't approve requests for same or higher level churches
        if ($this->level->getLevel() >= $requestingChurch->level->getLevel()) {
            return false;
        }

        // Must be an ancestor (in the hierarchy) of the requesting church
        return $this->isAncestorOf($requestingChurch);
    }

    public function isAncestorOf(Church $church): bool
    {
        $current = $church->parentChurch;

        while ($current) {
            if ($current->id === $this->id) {
                return true;
            }
            $current = $current->parentChurch;
        }

        return false;
    }

    public function getHierarchyPath(): array
    {
        $path = [$this];
        $current = $this->parentChurch;

        while ($current) {
            array_unshift($path, $current);
            $current = $current->parentChurch;
        }

        return $path;
    }

    public function getAllDescendants(): \Illuminate\Database\Eloquent\Collection
    {
        $descendants = new \Illuminate\Database\Eloquent\Collection();

        foreach ($this->childChurches as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getAllDescendants());
        }

        return $descendants;
    }

    // Query Scopes for Performance
    public function scopeWithHierarchy($query)
    {
        return $query->with(['parentChurch', 'childChurches', 'leaders.user']);
    }

    public function scopeByLevel($query, ChurchLevel $level)
    {
        return $query->where('level', $level);
    }

    public function scopeActive($query)
    {
        return $query->whereNull('deleted_at');
    }

    public function scopeWithUserCounts($query)
    {
        return $query->withCount(['users', 'users as active_users_count' => function ($q) {
            $q->where('is_active', true);
        }]);
    }

    public function scopeWithLeadershipInfo($query)
    {
        return $query->with(['leaders' => function ($q) {
            $q->with('user:id,full_name,email,phone_number');
        }]);
    }

    // Optimized methods
    public function getDescendantsOptimized(): \Illuminate\Database\Eloquent\Collection
    {
        // Use recursive CTE for better performance on large hierarchies
        return DB::table('churches')
            ->select('*')
            ->whereRaw("
                WITH RECURSIVE church_tree AS (
                    SELECT * FROM churches WHERE id = ?
                    UNION ALL
                    SELECT c.* FROM churches c
                    INNER JOIN church_tree ct ON c.parent_church_id = ct.id
                )
                SELECT * FROM church_tree WHERE id != ?
            ", [$this->id, $this->id])
            ->get()
            ->map(fn($church) => (new static)->newFromBuilder((array) $church));
    }

    public function getAncestorsOptimized(): \Illuminate\Database\Eloquent\Collection
    {
        // Use recursive CTE for better performance
        return DB::table('churches')
            ->select('*')
            ->whereRaw("
                WITH RECURSIVE church_ancestors AS (
                    SELECT * FROM churches WHERE id = ?
                    UNION ALL
                    SELECT c.* FROM churches c
                    INNER JOIN church_ancestors ca ON c.id = ca.parent_church_id
                )
                SELECT * FROM church_ancestors WHERE id != ?
            ", [$this->parent_church_id ?? 0, $this->id])
            ->get()
            ->map(fn($church) => (new static)->newFromBuilder((array) $church));
    }

    /**
     * Upgrade church to the next level and update all users
     */
    public function upgradeToNextLevel(): bool
    {
        $nextLevel = $this->level->getNextLevel();
        if (!$nextLevel) {
            return false; // Already at highest level
        }

        DB::transaction(function () use ($nextLevel) {
            // Update church level
            $this->update(['level' => $nextLevel]);

            // Update all users in this church to appropriate roles
            $this->upgradeUsersToNextLevel();
        });

        return true;
    }

    /**
     * Sync church leaders based on user roles
     */
    public function syncLeadersFromUserRoles(): void
    {
        // Get all users in this church with leadership roles
        $leadershipRoles = $this->level->getRolesByLevel();

        $users = $this->users()->whereHas('roles', function ($query) use ($leadershipRoles) {
            $query->whereIn('name', $leadershipRoles);
        })->with('roles')->get();

        // Clear existing leaders
        $this->leaders()->delete();

        // Create new leader records based on user roles
        foreach ($users as $user) {
            foreach ($user->roles as $role) {
                if (in_array($role->name, $leadershipRoles)) {
                    $this->leaders()->create([
                        'user_id' => $user->id,
                        'role' => $role->name,
                        'position' => $role->name, // Use role name as position
                        'appointed_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Get church leaders automatically from user roles
     */
    public function getLeadersFromRoles()
    {
        $leadershipRoles = $this->level->getRolesByLevel();

        return $this->users()->whereHas('roles', function ($query) use ($leadershipRoles) {
            $query->whereIn('name', $leadershipRoles);
        })->with('roles')->get()->map(function ($user) {
            return (object) [
                'id' => $user->id,
                'user' => $user,
                'role' => $user->roles->first()->name ?? 'Member',
                'position' => $user->roles->first()->name ?? 'Member',
                'appointed_at' => $user->created_at,
            ];
        });
    }

    /**
     * Upgrade all users in this church to roles appropriate for the new level
     */
    public function upgradeUsersToNextLevel(): void
    {
        $roleMapping = ChurchLevel::getRoleUpgradeMapping();

        $this->users()->chunk(100, function ($users) use ($roleMapping) {
            foreach ($users as $user) {
                $currentRole = $user->role;

                // Check if there's a mapping for the current role
                if (isset($roleMapping[$currentRole])) {
                    $newRole = $roleMapping[$currentRole];

                    // Update the user's role field
                    $user->update(['role' => $newRole]);

                    // Update Spatie role if the role exists
                    if (\Spatie\Permission\Models\Role::where('name', $newRole)->exists()) {
                        $user->syncRoles([$newRole]);
                    }
                }
            }
        });
    }

    // Financial Business Logic Methods
    public function getFinancialBalance(): FinancialBalance
    {
        return $this->financialBalance ?: FinancialBalance::getOrCreateForChurch($this->id);
    }

    public function hasMinimumBalanceForUpgrade(): bool
    {
        $minimumBalances = [
            ChurchLevel::BRANCH->value => 500000, // 500,000 TZS
            ChurchLevel::PARISH->value => 1000000, // 1,000,000 TZS
            ChurchLevel::LOCAL->value => 2000000, // 2,000,000 TZS
            ChurchLevel::DIOCESE->value => 5000000, // 5,000,000 TZS
        ];

        $requiredBalance = $minimumBalances[$this->level->value] ?? 0;
        return $this->getFinancialBalance()->hasMinimumBalance($requiredBalance);
    }

    public function canCreateContribution(): bool
    {
        // Only upper levels can create contributions for lower levels
        return in_array($this->level, [
            ChurchLevel::NATIONAL,
            ChurchLevel::REGIONAL,
            ChurchLevel::LOCAL,
            ChurchLevel::PARISH
        ]);
    }

    public function getContributionTargetLevels(): array
    {
        return match ($this->level) {
            ChurchLevel::NATIONAL => [ChurchLevel::REGIONAL, ChurchLevel::LOCAL, ChurchLevel::PARISH, ChurchLevel::BRANCH],
            ChurchLevel::REGIONAL => [ChurchLevel::LOCAL, ChurchLevel::PARISH, ChurchLevel::BRANCH],
            ChurchLevel::LOCAL => [ChurchLevel::PARISH, ChurchLevel::BRANCH],
            ChurchLevel::PARISH => [ChurchLevel::BRANCH],
            ChurchLevel::BRANCH => [],
        };
    }

    public function getRevenueTargetChurch(): ?Church
    {
        // Revenue flows upward in hierarchy
        return $this->parentChurch()->first();
    }

    public function hasCompleteFinancialInfo(): bool
    {
        return !empty($this->bank_account_number) || !empty($this->mobile_money_number);
    }

    public function getPaymentMethods(): array
    {
        $methods = [];

        if (!empty($this->bank_account_number)) {
            $methods[] = [
                'type' => 'bank_transfer',
                'label' => 'Bank Transfer',
                'details' => [
                    'bank_name' => $this->bank_name,
                    'account_number' => $this->bank_account_number,
                    'account_name' => $this->bank_account_name,
                ]
            ];
        }

        if (!empty($this->mobile_money_number)) {
            $methods[] = [
                'type' => 'mobile_money',
                'label' => 'Mobile Money',
                'details' => [
                    'provider' => $this->mobile_money_provider,
                    'number' => $this->mobile_money_number,
                ]
            ];
        }

        return $methods;
    }

    /**
     * Get churches that this church can create requests for (same level or lower)
     */
    public function getChurchesForRequests()
    {
        $currentLevel = $this->level->getLevel();
        $thisChurchId = $this->id;
        $thisParentId = $this->parent_church_id;
        $thisLevel = $this->level;

        return Church::where(function ($query) use ($currentLevel, $thisChurchId, $thisParentId, $thisLevel) {
            // Get all church levels that are same or lower
            $validLevels = [];
            foreach (ChurchLevel::cases() as $level) {
                if ($level->getLevel() >= $currentLevel) {
                    $validLevels[] = $level;
                }
            }

            $query->whereIn('level', $validLevels);

            // If not National level, also filter by hierarchy
            if ($thisLevel !== ChurchLevel::NATIONAL) {
                $query->where(function ($q) use ($thisChurchId, $thisParentId) {
                    // Same church
                    $q->where('id', $thisChurchId)
                      // Or descendant churches
                      ->orWhere('parent_church_id', $thisChurchId)
                      // Or churches under the same parent (siblings)
                      ->orWhere('parent_church_id', $thisParentId);
                });
            }
        })->get();
    }



    // Boot method to add validation
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($church) {
            if (!$church->validateHierarchy()) {
                throw ValidationException::withMessages([
                    'level' => 'Invalid church hierarchy. This level cannot be a child of the selected parent church.'
                ]);
            }
        });
    }
}
