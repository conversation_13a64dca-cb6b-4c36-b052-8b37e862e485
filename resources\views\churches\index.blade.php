@extends('layouts.app')

@section('title', __('churches.index_title'))
@section('page-title', __('churches.church_management'))

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">{{ __('churches.churches') }}</span>
    </li>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                <i class="fas fa-church mr-2 text-blue-600"></i>
                {{ __('churches.church_management') }}
            </h2>
            <p class="mt-1 text-sm text-gray-500">
                {{ __('churches.manage_churches_hierarchy') }}
            </p>
        </div>
        <div class="mt-4 flex space-x-3 md:mt-0 md:ml-4">
            @can('view-churches')
            <a href="{{ route('churches.trashed') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-trash mr-2"></i>
                {{ __('churches.trashed_churches') }}
            </a>
            @endcan

            @can('create-churches')
            <a href="{{ route('churches.create') }}"
               class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-plus mr-2"></i>
                {{ __('churches.add_church') }}
            </a>
            @endcan
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" action="{{ route('churches.index') }}" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700">{{ __('common.search') }}</label>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               name="search"
                               id="search"
                               value="{{ request('search') }}"
                               placeholder="{{ __('churches.search_churches') }}"
                               class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>

                <!-- Level Filter -->
                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="level" class="block text-sm font-medium text-gray-700">{{ __('churches.church_level') }}</label>
                    <select name="level"
                            id="level"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">{{ __('churches.all_levels') }}</option>
                        @foreach(['National', 'Regional', 'Local', 'Parish', 'Branch'] as $level)
                            <option value="{{ $level }}" {{ request('level') == $level ? 'selected' : '' }}>
                                {{ __('churches.' . strtolower($level)) }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Parent Church Filter -->
                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="parent" class="block text-sm font-medium text-gray-700">{{ __('churches.parent_church') }}</label>
                    <select name="parent"
                            id="parent"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">{{ __('churches.all_parents') }}</option>
                        @foreach($parentChurches ?? [] as $parent)
                            <option value="{{ $parent->id }}" {{ request('parent') == $parent->id ? 'selected' : '' }}>
                                {{ $parent->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Buttons -->
                <div class="flex space-x-2">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-search mr-2"></i>
                        {{ __('common.filter') }}
                    </button>
                    <a href="{{ route('churches.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-times mr-2"></i>
                        {{ __('common.clear') }}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Hierarchical Navigation -->
    <div class="bg-white shadow rounded-lg" x-data="hierarchicalNavigation()">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    <i class="fas fa-sitemap mr-2 text-blue-600"></i>
                    {{ __('churches.hierarchical_navigation') }}
                </h3>
                <button @click="resetNavigation()"
                        class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                    {{ __('common.reset') }}
                </button>
            </div>

            <!-- Breadcrumb -->
            <div x-show="breadcrumb.length > 0" class="mb-4">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-2">
                        <li>
                            <button @click="resetNavigation()"
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                {{ __('churches.all_churches') }}
                            </button>
                        </li>
                        <template x-for="(item, index) in breadcrumb" :key="index">
                            <li class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                                <button @click="navigateToLevel(index)"
                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                        x-text="item.name">
                                </button>
                            </li>
                        </template>
                    </ol>
                </nav>
            </div>

            <!-- Navigation Buttons -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <button @click="loadRegions()"
                        class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-map mr-2 text-blue-600"></i>
                    {{ __('churches.view_regions') }}
                </button>

                <button @click="loadByLevel('Local')"
                        class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-church mr-2 text-green-600"></i>
                    {{ __('churches.view_local') }}
                </button>

                <button @click="loadByLevel('Parish')"
                        class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-home mr-2 text-purple-600"></i>
                    {{ __('churches.view_parishes') }}
                </button>

                <button @click="loadByLevel('Branch')"
                        class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-code-branch mr-2 text-orange-600"></i>
                    {{ __('churches.view_branches') }}
                </button>
            </div>

            <!-- Loading State -->
            <div x-show="loading" class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
                <p class="mt-2 text-sm text-gray-600">{{ __('common.loading') }}...</p>
            </div>

            <!-- Hierarchical Results -->
            <div x-show="!loading && hierarchicalChurches.length > 0" class="space-y-3">
                <template x-for="church in hierarchicalChurches" :key="church.id">
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <h4 class="text-sm font-medium text-gray-900" x-text="church.name"></h4>
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                      :class="getLevelBadgeClass(church.level)"
                                      x-text="church.level">
                                </span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                <span x-text="church.location"></span>
                                <span class="mx-2">•</span>
                                <i class="fas fa-users mr-1"></i>
                                <span x-text="church.users_count"></span> {{ __('common.users') }}
                                <span x-show="church.children_count > 0" class="mx-2">•</span>
                                <span x-show="church.children_count > 0">
                                    <i class="fas fa-sitemap mr-1"></i>
                                    <span x-text="church.children_count"></span> {{ __('churches.sub_churches') }}
                                </span>
                            </p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button x-show="church.has_children"
                                    @click="loadChildren(church)"
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                {{ __('churches.view_children') }} →
                            </button>
                            <a :href="`{{ url('churches') }}/${church.id}`"
                               class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                {{ __('common.view') }}
                            </a>
                        </div>
                    </div>
                </template>
            </div>

            <!-- No Results -->
            <div x-show="!loading && hierarchicalChurches.length === 0 && hasSearched"
                 class="text-center py-8">
                <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">{{ __('churches.no_churches_found') }}</p>
            </div>
        </div>
    </div>

    <!-- Churches Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    {{ __('churches.churches_list') }}
                </h3>
                <p class="text-sm text-gray-500">
                    {{ $churches->total() }} {{ __('churches.total_churches_count') }}
                </p>
            </div>
        </div>

        @if($churches->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Church
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Level & Hierarchy
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Location & Region
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contact Info
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Statistics
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($churches as $church)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-church text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ $church->name }}
                                    </div>
                                    @if($church->date_established)
                                    <div class="text-sm text-gray-500">
                                        Est. {{ $church->date_established ? $church->date_established->format('Y') : 'Unknown' }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($church->level->value == 'National') bg-purple-100 text-purple-800
                                    @elseif($church->level->value == 'Regional') bg-blue-100 text-blue-800
                                    @elseif($church->level->value == 'Local') bg-green-100 text-green-800
                                    @elseif($church->level->value == 'Parish') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ $church->level->value }}
                                </span>
                                @if($church->parentChurch)
                                <div class="text-xs text-gray-500 mt-1">
                                    Under: {{ Str::limit($church->parentChurch->name, 20) }}
                                </div>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt mr-2 text-gray-400"></i>
                                    {{ $church->location }}
                                </div>
                                @if($church->district || $church->region)
                                <div class="text-xs text-gray-500 mt-1">
                                    @if($church->district)
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-map mr-1"></i>
                                            {{ $church->district }}
                                        </span>
                                    @endif
                                    @if($church->district && $church->region) • @endif
                                    @if($church->region)
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-globe-africa mr-1"></i>
                                            {{ $church->region }}
                                        </span>
                                    @endif
                                </div>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                @if($church->phone_number || $church->email)
                                    <div class="space-y-1">
                                        @if($church->phone_number)
                                        <div class="flex items-center text-xs">
                                            <i class="fas fa-phone mr-2 text-blue-500"></i>
                                            <a href="tel:{{ $church->phone_number }}" class="hover:text-blue-600">
                                                {{ Str::limit($church->phone_number, 15) }}
                                            </a>
                                        </div>
                                        @endif
                                        @if($church->email)
                                        <div class="flex items-center text-xs">
                                            <i class="fas fa-envelope mr-2 text-green-500"></i>
                                            <a href="mailto:{{ $church->email }}" class="hover:text-green-600">
                                                {{ Str::limit($church->email, 20) }}
                                            </a>
                                        </div>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-xs text-gray-400">No contact info</span>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-users mr-1 text-gray-400"></i>
                                        <span>{{ $church->users_count ?? 0 }}</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-church mr-1 text-gray-400"></i>
                                        <span>{{ $church->children_count ?? 0 }}</span>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($church->deleted_at)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-trash mr-1"></i>
                                    Trashed
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Active
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                @can('view-churches')
                                <a href="{{ route('churches.show', $church) }}"
                                   class="text-blue-600 hover:text-blue-900"
                                   title="View Church">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @endcan

                                @if(!$church->deleted_at)
                                    @can('edit-churches')
                                    <a href="{{ route('churches.edit', $church) }}"
                                       class="text-indigo-600 hover:text-indigo-900"
                                       title="Edit Church">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endcan

                                    @can('delete-churches')
                                    <form action="{{ route('churches.destroy', $church) }}"
                                          method="POST"
                                          class="inline"
                                          onsubmit="return confirm('Are you sure you want to delete this church?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="text-red-600 hover:text-red-900"
                                                title="Delete Church">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    @endcan
                                @endif
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            {{ $churches->appends(request()->query())->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <i class="fas fa-church text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No churches found</h3>
            <p class="text-gray-500 mb-4">
                @if(request()->hasAny(['search', 'level', 'parent']))
                    No churches match your current filters.
                @else
                    Get started by adding your first church.
                @endif
            </p>
            @can('create-churches')
            <a href="{{ route('churches.create') }}"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>
                Add Church
            </a>
            @endcan
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
    function hierarchicalNavigation() {
        return {
            loading: false,
            hasSearched: false,
            hierarchicalChurches: [],
            breadcrumb: [],

            async loadRegions() {
                this.loading = true;
                this.hasSearched = true;
                this.breadcrumb = [];

                try {
                    const response = await fetch('{{ route("churches.regions") }}');
                    const data = await response.json();
                    this.hierarchicalChurches = data.regions;
                } catch (error) {
                    console.error('Error loading regions:', error);
                    this.hierarchicalChurches = [];
                } finally {
                    this.loading = false;
                }
            },

            async loadByLevel(level) {
                this.loading = true;
                this.hasSearched = true;
                this.breadcrumb = [];

                try {
                    const response = await fetch(`{{ route("churches.hierarchical-data") }}?level=${level}`);
                    const data = await response.json();
                    this.hierarchicalChurches = data.churches;
                } catch (error) {
                    console.error('Error loading churches by level:', error);
                    this.hierarchicalChurches = [];
                } finally {
                    this.loading = false;
                }
            },

            async loadChildren(church) {
                this.loading = true;

                // Add to breadcrumb
                this.breadcrumb.push({
                    id: church.id,
                    name: church.name,
                    level: church.level
                });

                try {
                    const response = await fetch(`{{ route("churches.hierarchical-data") }}?parent_id=${church.id}`);
                    const data = await response.json();
                    this.hierarchicalChurches = data.churches;
                } catch (error) {
                    console.error('Error loading children:', error);
                    this.hierarchicalChurches = [];
                } finally {
                    this.loading = false;
                }
            },

            async navigateToLevel(index) {
                if (index === -1) {
                    this.resetNavigation();
                    return;
                }

                const targetChurch = this.breadcrumb[index];
                this.breadcrumb = this.breadcrumb.slice(0, index + 1);

                this.loading = true;

                try {
                    const response = await fetch(`{{ route("churches.hierarchical-data") }}?parent_id=${targetChurch.id}`);
                    const data = await response.json();
                    this.hierarchicalChurches = data.churches;
                } catch (error) {
                    console.error('Error navigating to level:', error);
                    this.hierarchicalChurches = [];
                } finally {
                    this.loading = false;
                }
            },

            resetNavigation() {
                this.hierarchicalChurches = [];
                this.breadcrumb = [];
                this.hasSearched = false;
                this.loading = false;
            },

            getLevelBadgeClass(level) {
                const classes = {
                    'National': 'bg-red-100 text-red-800',
                    'Regional': 'bg-blue-100 text-blue-800',
                    'Local': 'bg-green-100 text-green-800',
                    'Parish': 'bg-purple-100 text-purple-800',
                    'Branch': 'bg-orange-100 text-orange-800'
                };
                return classes[level] || 'bg-gray-100 text-gray-800';
            }
        }
    }
</script>
@endpush

@endsection